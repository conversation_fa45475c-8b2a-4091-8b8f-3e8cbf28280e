import * as fs from 'fs';
import * as path from 'path';
import {
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { InjectPage } from 'nestjs-puppeteer';
import { Page } from 'puppeteer';
import {
  processAndSaveCSVFile,
  isDownloadAllowed,
  getCSVJson,
} from '../shared/utils/file.utils';
import { IntelligenceCampaigns } from '../shared/types';
import { extractBiddingData } from '../shared/utils/campaign.utils';

@Injectable()
export class CampaignService {
  private readonly logger = new Logger('CampaignService');
  private readonly CAMPAIGNS_FILE = 'campaigns.csv';
  private readonly CAMPAIGNS_URL = 'https://intelligence.trivago.com/campaigns';

  constructor(@InjectPage('intelligence') private readonly page: Page) {}

  async getCampaigns() {
    try {
      const campaignsPath = path.join(
        __dirname,
        `../../files/${this.CAMPAIGNS_FILE}`,
      );

      if (
        isDownloadAllowed(this.CAMPAIGNS_FILE) ||
        !fs.existsSync(campaignsPath)
      ) {
        await processAndSaveCSVFile(
          null,
          fs.readFileSync(campaignsPath),
          this.CAMPAIGNS_FILE,
        );
      }

      const intelligenceCampaigns: IntelligenceCampaigns = await getCSVJson(
        this.CAMPAIGNS_FILE,
      );
      const campaigns = intelligenceCampaigns
        .map((campaign) => {
          if (campaign.biddingType != 'Manual')
            return extractBiddingData(campaign);
        })
        .filter((campaign) => campaign != null);

      return campaigns;
    } catch (err) {
      console.log(err);
      throw new InternalServerErrorException();
    }
  }

  async fetchCampaigns() {
    try {
      let logged = false;

      while (!logged) {
        try {
          await Promise.all([
            this.page.waitForSelector(
              'button[data-qa*="export-report-button"]',
            ),
            this.page.goto('https://intelligence.trivago.com/campaigns', {
              waitUntil: 'domcontentloaded',
            }),
          ]);

          logged = true;
        } catch (err) {
          // Handle login if needed
        }
      }

      const dropdownButton = await this.page.$(
        'button[data-qa*="export-report-button"]',
      );
      await dropdownButton?.click();

      const downloadButton = await this.page.$(
        'li[data-qa*="dropdown-option-0"]',
      );
      await downloadButton?.click();

      const file = await this.page.waitForResponse(async (response) =>
        response
          .url()
          .includes(
            'https://intelligence.trivago.com/v1/analytics/export/download?view=campaigns',
          ),
      );

      await new Promise((resolver) => setTimeout(resolver, 5000));

      await processAndSaveCSVFile(file, null, this.CAMPAIGNS_FILE);
    } catch (err) {
      throw new InternalServerErrorException('Falha ao baixar campanhas!');
    }
  }
}
