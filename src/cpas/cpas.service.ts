import * as fs from 'fs';
import * as path from 'path';
import {
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { InjectPage } from 'nestjs-puppeteer';
import { Page } from 'puppeteer';
import {
  processAndSaveCSVFile,
  isDownloadAllowed,
  getCSVJson,
  getLastDownload,
  parseCSVFile,
  writeCSVFile,
} from '../shared/utils/file.utils';
import { validateCPAsCampaignFile } from '../shared/utils/cpas.utils';
import { InventoryService } from '../inventory/inventory.service';

@Injectable()
export class CpasService {
  private readonly logger = new Logger('CpasService');
  private readonly CPAS_FILE = 'cpas.csv';
  private readonly HISTORY_URL =
    'https://intelligence.trivago.com/history?tab=CPA';
  private readonly CAMPAIGNS_URL = 'https://intelligence.trivago.com/campaigns';
  private readonly CPAS_REQUEST_URL =
    'https://intelligence.trivago.com/v1/bidding/source-file?jobId';
  private readonly CPAS_DROPDOWN_SELECTOR =
    'select[class*="c-history-toolbar"]';
  private readonly CPAS_CALENDAR_SELECTOR = '.c-calendar-selector';
  private readonly CPAS_CALENDAR_STARTDATE_SELECTOR =
    '.c-datePicker .c-datePicker__input';
  private readonly CPAS_CALENDAR_APPLY_BUTTON_SELECTOR = '.c-datePicker button';
  private readonly CPAS_DROPDOWN_OPTION_SELECTOR =
    'div[class*="history-event-file"] > button';
  private readonly CPAS_DOWNLOAD_BUTTON_SELECTOR =
    'div[class*="history-event-files_downloadMenu"] li';

  constructor(
    @InjectPage('intelligence') private readonly page: Page,
    private readonly inventoryService: InventoryService,
  ) {}

  getCPAsLastUpdate = () => {
    try {
      return getLastDownload(this.CPAS_FILE);
    } catch (err) {
      throw new InternalServerErrorException();
    }
  };

  async getCPAs() {
    try {
      const cpasPath = path.join(__dirname, `../../../files/${this.CPAS_FILE}`);

      if (isDownloadAllowed(this.CPAS_FILE) || !fs.existsSync(cpasPath)) {
        await processAndSaveCSVFile(
          null,
          fs.readFileSync(cpasPath),
          this.CPAS_FILE,
        );
      }

      const cpas = await getCSVJson(this.CPAS_FILE);
      return cpas;
    } catch (err) {
      throw new InternalServerErrorException();
    }
  }

  async fetchCPAs() {
    const downloadsPath = path.join(__dirname, `../downloads`);

    try {
      let logged = false;

      while (!logged) {
        try {
          await Promise.all([
            this.page.waitForSelector(this.CPAS_DROPDOWN_SELECTOR),
            this.page.goto(this.HISTORY_URL, {
              waitUntil: 'domcontentloaded',
            }),
          ]);

          logged = true;
        } catch (err) {
          // Handle login if needed
        }
      }

      const dropdown = await this.page.$(this.CPAS_DROPDOWN_SELECTOR);
      await this.page.select(
        this.CPAS_DROPDOWN_SELECTOR,
        'CAMPAIGN_ALLOCATION',
      );

      const calendarSelector = await this.page.$(this.CPAS_CALENDAR_SELECTOR);
      await calendarSelector?.click();

      const startDateInput = await this.page.$(
        this.CPAS_CALENDAR_STARTDATE_SELECTOR,
      );

      await startDateInput?.click({ clickCount: 3 });

      const today = new Date();
      const yesterday = new Date(today);
      yesterday.setDate(today.getDate() - 1);

      const formattedDate = yesterday.toLocaleDateString('en-GB');

      await startDateInput?.type(formattedDate);

      const applyButton = await this.page.$(
        this.CPAS_CALENDAR_APPLY_BUTTON_SELECTOR,
      );
      await applyButton?.click();

      await this.page.waitForSelector(this.CPAS_DROPDOWN_OPTION_SELECTOR);

      const dropdownOption = await this.page.$(
        this.CPAS_DROPDOWN_OPTION_SELECTOR,
      );
      await dropdownOption?.click();

      const downloadButton = await this.page.$(
        this.CPAS_DOWNLOAD_BUTTON_SELECTOR,
      );
      await downloadButton?.click();

      const fileRequest = await this.page.waitForResponse(async (response) =>
        response.url().includes(this.CPAS_REQUEST_URL),
      );

      const fileRequestUrl = fileRequest.url();
      const urlParams = new URLSearchParams(fileRequestUrl.split('?')[1]);
      const jobId = urlParams.get('jobId');

      await new Promise((resolver) => setTimeout(resolver, 5000));
      const file = fs.readFileSync(path.join(downloadsPath, `${jobId}.csv`));

      await processAndSaveCSVFile(file, null, this.CPAS_FILE);
    } catch (err) {
      throw new InternalServerErrorException(
        "Falha ao baixar arquivo de CPA's!",
      );
    }
  }

  async updateCPAFile(defaultCampaign: string) {
    try {
      const inventory = await this.inventoryService.getInventory();
      const cpas = await this.getCPAs();

      const cpasFilePath = path.join(__dirname, '../files', this.CPAS_FILE);

      const cpasData = await parseCSVFile(cpasFilePath, ',');

      const cpasMap = new Map();
      cpasData.forEach((cpa) => {
        cpasMap.set(cpa.partner_reference, cpa);
      });

      const updatedCpas = [];

      inventory.forEach((hotel) => {
        const existingCpa = cpasMap.get(hotel.partnerReference);
        if (existingCpa) {
          updatedCpas.push(existingCpa);
        } else {
          updatedCpas.push({
            partner_reference: hotel.partnerReference,
            locale: 'BR',
            campaign: defaultCampaign,
          });
        }
      });

      await writeCSVFile(cpasFilePath, updatedCpas);

      const contentToAppend = updatedCpas
        .map((cpa) => `${cpa.partner_reference},${cpa.locale},${cpa.campaign}`)
        .join('\n');

      fs.appendFileSync(cpasFilePath, contentToAppend);
    } catch (err) {
      throw new InternalServerErrorException();
    }
  }

  async uploadNewCPAFile(filePath) {
    try {
      let logged = false;

      while (!logged) {
        try {
          await Promise.all([
            this.page.waitForSelector('button[data-qa="upload-bids"]'),
            this.page.goto(this.CAMPAIGNS_URL, {
              waitUntil: 'domcontentloaded',
            }),
          ]);

          logged = true;
        } catch (err) {
          // Handle login if needed
        }
      }

      const uploadDropdownButton = await this.page.$(
        'button[data-qa="upload-bids"]',
      );
      await uploadDropdownButton.click();

      const inputFile = await this.page.$(
        'label[data-qa="file-uploader-CAMPAIGN_ALLOCATION"] input[class="c-file-uploader__input"]',
      );
      await inputFile.uploadFile(filePath);
    } catch (err) {
      throw new InternalServerErrorException(
        "Falha ao fazer upload do novo CPA's!",
      );
    }
  }

  async validateCampaignFile() {
    try {
      await validateCPAsCampaignFile();
      return true;
    } catch (err) {
      throw new InternalServerErrorException(err);
    }
  }
}
