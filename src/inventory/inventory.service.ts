import * as fs from 'fs';
import * as path from 'path';
import {
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { InjectPage } from 'nestjs-puppeteer';
import { Page } from 'puppeteer';
import {
  processAndSaveCSVFile,
  isDownloadAllowed,
  extractArtifactPath,
  getCSVJson,
  getLastDownload,
} from '../shared/utils/file.utils';
import { Inventory } from '../shared/types';

@Injectable()
export class InventoryService {
  private readonly logger = new Logger('InventoryService');
  private readonly INVENTORY_FILE = 'inventory.csv';
  private readonly ACTIVE_INVENTORY_URL =
    'https://intelligence.trivago.com/reports/inventory-matched-active-report?reportDefinitionId=5&isCombined=false';
  private readonly INVENTORY_REQUEST_URL =
    'https://intelligence.trivago.com/v1/reports/artifacts/3620/download';
  private readonly INVENTORY_DOWNLOAD_BUTTON_SELECTOR = 'td > .e-button';

  constructor(@InjectPage('intelligence') private readonly page: Page) {}

  async getInventory() {
    try {
      const inventoryPath = path.join(
        __dirname,
        `../../../files/${this.INVENTORY_FILE}`,
      );

      if (
        isDownloadAllowed(this.INVENTORY_FILE) ||
        !fs.existsSync(inventoryPath)
      ) {
        await processAndSaveCSVFile(
          null,
          fs.readFileSync(inventoryPath),
          this.INVENTORY_FILE,
        );
      }

      const inventory = await getCSVJson(this.INVENTORY_FILE);
      return inventory;
    } catch (err) {
      throw new InternalServerErrorException();
    }
  }

  async fetchInventory() {
    const downloadsPath = path.join(__dirname, `../downloads`);

    try {
      let logged = false;

      try {
        await this.page.goto(this.ACTIVE_INVENTORY_URL, {
          waitUntil: 'domcontentloaded',
        });
        await this.page.waitForSelector(
          this.INVENTORY_DOWNLOAD_BUTTON_SELECTOR,
        );

        logged = true;
      } catch (err) {
        console.log('unable to goto', err, this.page.url());
      }

      const inventoryButton = await this.page.$(
        this.INVENTORY_DOWNLOAD_BUTTON_SELECTOR,
      );

      await inventoryButton?.click();

      const fileRequest = await this.page.waitForResponse(async (response) =>
        response.url().includes(this.INVENTORY_REQUEST_URL),
      );

      const fileRequestUrl = fileRequest.url();
      const fileName = extractArtifactPath(fileRequestUrl)
        .split('/')
        .reverse()[0];

      await new Promise((resolver) => setTimeout(resolver, 5000));
      const file = fs.readFileSync(path.join(downloadsPath, fileName));

      await processAndSaveCSVFile(file, null, this.INVENTORY_FILE);
    } catch (err) {
      throw new InternalServerErrorException(
        'Falha ao baixar arquivo de inventário!',
      );
    }
  }

  getInventoryLastUpdate = () => {
    try {
      return getLastDownload(this.INVENTORY_FILE);
    } catch (err) {
      throw new InternalServerErrorException();
    }
  };

  async getHotelByHotelId(id: string) {
    try {
      const inventory: Inventory = await this.getInventory();

      const hotel = inventory.find((hotel) => hotel.partnerReference === id);
      return hotel;
    } catch (err) {
      throw new InternalServerErrorException();
    }
  }

  async getHotelByTrivagoId(id: string) {
    try {
      const inventory: Inventory = await this.getInventory();

      const hotel = inventory.find((hotel) => hotel.trivagoItemID === id);
      return hotel;
    } catch (err) {
      throw new InternalServerErrorException();
    }
  }
}
